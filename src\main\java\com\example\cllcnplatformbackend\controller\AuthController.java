package com.example.cllcnplatformbackend.controller;

import com.example.cllcnplatformbackend.utils.Result;
import com.example.cllcnplatformbackend.dto.LoginRequest;
import com.example.cllcnplatformbackend.dto.LoginResponse;
import com.example.cllcnplatformbackend.service.AuthService;
import com.example.cllcnplatformbackend.utils.UserContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 客户端认证控制器
 * 提供客户端用户登录、令牌刷新等认证功能
 * 支持员工和客户登录（管理后台请使用AdminAuthController）
 *
 * <AUTHOR> Platform
 * @since 2025-07-31
 * @updated 2025-08-02 (分离管理后台认证)
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthService authService;
    private final com.example.cllcnplatformbackend.service.LoginAttemptService loginAttemptService;

    /**
     * 客户端用户登录
     * 支持员工和客户通过邮箱或手机号登录
     * 注意：管理后台登录请使用 /admin/auth/login
     */
    @PostMapping("/login")
    public Result<LoginResponse> login(@RequestBody @Valid LoginRequest loginRequest) {
        try {
            log.info("用户登录请求 - 账号: {}", loginRequest.getAccount());
            
            LoginResponse response = authService.login(loginRequest);
            
            log.info("用户登录成功 - 平台用户ID: {}", response.getUserInfo().getPlatformUserId());
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("用户登录失败 - 账号: {}, 错误: {}", loginRequest.getAccount(), e.getMessage());
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 管理后台登录
     * 只允许员工且role为admin的用户登录
     */
    @PostMapping("/admin/login")
    public Result<LoginResponse> adminLogin(@RequestBody @Valid LoginRequest loginRequest) {
        try {
            log.info("管理后台登录请求 - 账号: {}", loginRequest.getAccount());

            LoginResponse response = authService.adminLogin(loginRequest);

            log.info("管理后台登录成功 - 平台用户ID: {}, 角色: {}",
                response.getUserInfo().getPlatformUserId(),
                response.getUserInfo().getUserType());
            return Result.success(response);

        } catch (Exception e) {
            log.error("管理后台登录失败 - 账号: {}, 错误: {}", loginRequest.getAccount(), e.getMessage());
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     * 需要JWT认证
     */
    @GetMapping("/me")
    public Result<LoginResponse.UserInfo> getCurrentUser() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            log.info("获取用户信息请求 - 用户ID: {}", currentUserId);
            
            LoginResponse.UserInfo userInfo = authService.getUserInfo(currentUserId);
            
            return Result.success(userInfo);
            
        } catch (Exception e) {
            log.error("获取用户信息失败 - 错误: {}", e.getMessage());
            return Result.error("获取用户信息失败：" + e.getMessage());
        }
    }

    /**
     * 刷新访问令牌
     */
    @PostMapping("/refresh")
    public Result<Map<String, Object>> refreshToken(
            @RequestBody Map<String, String> request) {
        try {
            String refreshToken = request.get("refreshToken");
            if (refreshToken == null || refreshToken.trim().isEmpty()) {
                return Result.validateFailed("刷新令牌不能为空");
            }
            
            log.info("刷新令牌请求");
            
            String newAccessToken = authService.refreshToken(refreshToken);
            
            Map<String, Object> response = Map.of(
                "accessToken", newAccessToken,
                "tokenType", "Bearer",
                "expiresIn", 7200L
            );
            
            log.info("令牌刷新成功");
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("刷新令牌失败 - 错误: {}", e.getMessage());
            return Result.error("刷新令牌失败：" + e.getMessage());
        }
    }

    /**
     * 用户登出
     * 客户端应该清除本地存储的令牌
     */
    @PostMapping("/logout")
    public Result<Void> logout() {
        try {
            String currentUserId = UserContext.getCurrentUserId();
            log.info("用户登出 - 用户ID: {}", currentUserId);
            
            // 这里可以添加令牌黑名单逻辑，暂时只记录日志
            
            return Result.success();
            
        } catch (Exception e) {
            log.error("用户登出失败 - 错误: {}", e.getMessage());
            return Result.error("登出失败：" + e.getMessage());
        }
    }
}
